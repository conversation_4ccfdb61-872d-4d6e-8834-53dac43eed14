from flask import Blueprint, render_template, abort, request, redirect, url_for, flash, current_app
from flask_login import login_required, current_user, logout_user
from app.models import Post, User
from app import db, bcrypt
from sqlalchemy import select
from werkzeug.utils import secure_filename
from datetime import datetime
import os
import uuid

bp = Blueprint('profile', __name__)


@bp.route('/profile')
@login_required
def profile():
    """Redirect to current user's profile page."""
    return profile_view(current_user.id)


@bp.route('/profile/<int:user_id>')
def profile_view(user_id):
    """Display a user's profile page with their posts. Can view any user's profile."""
    user = db.session.execute(select(User).where(User.id == user_id)).scalar_one_or_none()
    if not user:
        abort(404)

    user_posts = db.session.execute(
        select(Post).where(Post.user_id == user.id).order_by(Post.date_posted.desc())).scalars().all()

    is_own_profile = current_user.is_authenticated and current_user.id == user.id
    return render_template('profile.html', user=user, posts=user_posts, is_own_profile=is_own_profile)


@bp.route('/delete_account', methods=['POST'])
@login_required
def delete_account():
    """Delete user account after password verification. Removes all associated files and data."""
    password = request.form.get('password')

    if not bcrypt.check_password_hash(current_user.password, password):
        flash('Incorrect password. Account not deleted.')
        return redirect(url_for('profile.profile'))

    for post in current_user.posts:
        if post.image_file:
            image_path = os.path.join(current_app.config['UPLOAD_FOLDER'], post.image_file)
            if os.path.exists(image_path):
                os.remove(image_path)

    if current_user.profile_picture:
        profile_path = os.path.join(current_app.config['PROFILE_PICTURE_FOLDER'], current_user.profile_picture)
        if os.path.exists(profile_path):
            os.remove(profile_path)

    db.session.delete(current_user)
    db.session.commit()
    logout_user()

    flash('Your account has been successfully deleted.')
    return redirect(url_for('blog.home'))


@bp.route('/edit_profile', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """Edit current user's profile including bio and profile picture."""
    if request.method == 'POST':
        current_user.bio = request.form.get('bio', '')

        file = request.files.get('profile_picture')
        if file and file.filename:
            if current_user.profile_picture:
                old_path = os.path.join(current_app.config['PROFILE_PICTURE_FOLDER'], current_user.profile_picture)
                if os.path.exists(old_path):
                    os.remove(old_path)

            original_filename = secure_filename(file.filename)
            file_ext = os.path.splitext(original_filename)[1].lower()
            unique_filename = f"profile_{uuid.uuid4().hex}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{file_ext}"

            filepath = os.path.join(current_app.config['PROFILE_PICTURE_FOLDER'], unique_filename)
            file.save(filepath)
            current_user.profile_picture = unique_filename

        db.session.commit()
        flash('Profile updated successfully!')
        return redirect(url_for('profile.profile'))

    return render_template('edit_profile.html', user=current_user)
