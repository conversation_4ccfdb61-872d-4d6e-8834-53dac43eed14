{% extends 'base.html' %}
{% block content %}
<div class="p-4 mb-4 bg-dark border rounded text-center text-white">
  <h1 class="display-5 fw-bold">Edit Profile</h1>
</div>
<div class="border p-3 mb-2 rounded shadow-sm bg-white">
  <div class="mb-3 text-center">
    {% if user.profile_picture %}
      <img src="{{ url_for('static', filename='profile_picture_uploads/' ~ user.profile_picture) }}"
          class="img-fluid rounded-circle" style="width: 150px; height: 150px; object-fit: cover;"
          alt="Current Profile Picture">
    {% else %}
      <img src="{{ url_for('static', filename='profile_picture_uploads/' ~ 'default_profile_picture.webp') }}"
          class="img-fluid rounded-circle" style="width: 150px; height: 150px; object-fit: cover;"
          alt="Default Profile Picture">
    {% endif %}
  </div>

  <form method="post" enctype="multipart/form-data">
    <div class="mb-3">
      <label for="profile_picture" class="form-label">Profile Picture:</label>
      <input type="file" name="profile_picture" class="form-control" accept="image/*">
      <div class="form-text">Leave empty to keep current picture</div>
    </div>
    <div class="mb-3">
      <label for="bio" class="form-label">Bio:</label>
      <textarea name="bio" class="form-control" rows="4" placeholder="Tell us about yourself...">{{ user.bio or '' }}</textarea>
    </div>
    <button type="submit" class="btn btn-dark">Update Profile</button>
    <a href="{{ url_for('profile.profile') }}" class="btn btn-secondary">Cancel</a>
  </form>
</div>
{% endblock %}