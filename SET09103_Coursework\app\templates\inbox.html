{% extends 'base.html' %}
{% block content %}
<div class="p-4 mb-4 bg-dark border rounded text-center text-white">
  <h1 class="display-5 fw-bold">Inbox</h1>
  <a href="{{ url_for('messaging.compose') }}" class="btn btn-light">Compose Message</a>
</div>

{% if messages %}
    {% for message in messages %}
    <div class="card mb-3">
        <div class="card-body">
            <div class="d-flex justify-content-between">
                <h6 class="card-subtitle mb-2 text-muted">From: {{ message.sender.username }}</h6>
                <small class="text-muted">{{ message.timestamp.strftime('%Y-%m-%d %H:%M') }}</small>
            </div>
            <p class="card-text">{{ message.content }}</p>
            <a href="{{ url_for('messaging.send_message', user_id=message.sender.id) }}" class="btn btn-sm btn-dark">Reply</a>
        </div>
    </div>
    {% endfor %}
{% else %}
    <div class="text-center mt-5">
        <p class="text-muted">No messages yet.</p>
        <a href="{{ url_for('messaging.compose') }}" class="btn btn-dark">Send your first message</a>
    </div>
{% endif %}
{% endblock %}