{% extends 'base.html' %}
{% block content %}
<div class="p-4 mb-4 bg-dark border rounded text-center text-white">
  <h1 class="display-5 fw-bold">Edit Post</h1>
</div>

<div class="mb-3 text-center">
  <img src="{{ url_for('static', filename='blog_image_uploads/' ~ post.image_file) }}"
       class="img-fluid" style="max-height: 300px; object-fit: cover;"
       alt="{{ post.title }}">
  <p class="text-muted mt-2">Image cannot be changed</p>
</div>

<form method="post">
  <div class="mb-3">
    <label for="title" class="form-label">Title:</label>
    <input type="text" name="title" class="form-control" value="{{ post.title }}" required>
  </div>
  <div class="mb-3">
    <label for="content" class="form-label">Description:</label>
    <textarea name="content" class="form-control" rows="5" required>{{ post.content }}</textarea>
  </div>
  <button type="submit" class="btn btn-dark">Update Post</button>
  <a href="{{ url_for('blog.view_post', post_id=post.id) }}" class="btn btn-secondary">Cancel</a>
</form>
{% endblock %}