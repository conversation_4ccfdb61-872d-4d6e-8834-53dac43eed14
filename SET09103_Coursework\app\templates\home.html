{% extends 'base.html' %}
{% block content %}

<div class="p-4 mb-4 bg-dark border rounded text-center text-white">
  <h1 class="display-5 fw-bold">The IMGS/ Gallery</h1>
  <p class="lead">Photos that people with actual talent took...</p>
</div>

<div class="row">
  {% for post in posts %}
    <div class="card mb-3 shadow-sm">
      <div class="row g-0">
        <div class="col-md-4 text-center">
          <img src="{{ url_for('static', filename='blog_image_uploads/' ~ post.image_file) }}"
              class="img-fluid p-3" style="max-height: 200px; object-fit: contain;" alt="The subject of the blog post">
        </div>
        <div class="col-md-8">
          <div class="card-body">
            <h5 class="card-title">{{ post.title }}</h5>
            <h6 class="card-subtitle text-muted mb-2">By <a href="{{ url_for('profile.profile_view', user_id=post.author.id) }}" class="text-decoration-none">{{ post.author.username }}</a></h6>
            <p class="card-text">{{ post.content[:200] }}{% if post.content|length > 200 %}...{% endif %}</p>
            <a href="{{ url_for('blog.view_post', post_id=post.id) }}" class="btn btn-dark btn-large">View Full Post</a>
          </div>
        </div>
      </div>
    </div>
  {% endfor %}
</div>

{% endblock %}