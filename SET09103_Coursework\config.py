import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'i_should_really_change_this'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///IMGS.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    UPLOAD_FOLDER = 'app/static/blog_image_uploads'
    PROFILE_PICTURE_FOLDER = 'app/static/profile_picture_uploads'
    MAX_CONTENT_LENGTH = 5 * 1024 * 1024  # 5MB
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
