{% extends 'base.html' %}
{% block content %}
<div class="p-4 mb-4 bg-dark border rounded text-center text-white">
  <h1 class="display-5 fw-bold">Compose Message</h1>
</div>
<div class="border p-3 mb-2 rounded shadow-sm bg-white">
    <form method="post">
        <div class="mb-3">
            <label for="username" class="form-label">To (Username):</label>
            <input type="text" name="username" class="form-control" required>
        </div>
        <div class="mb-3">
            <label for="content" class="form-label">Message:</label>
            <textarea name="content" class="form-control" rows="5" required></textarea>
        </div>
        <button type="submit" class="btn btn-dark">Send Message</button>
        <a href="{{ url_for('messaging.inbox') }}" class="btn btn-secondary">Cancel</a>
    </form>
</div>
{% endblock %}