<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>IMGS/</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body class="bg-light">
  <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4 max">
    <div class="container">
      <a class="navbar-brand fw-bold" href="{{ url_for('blog.home') }}">IMGS/</a>
      <div class="d-flex">
        {% if current_user.is_authenticated %}
          <a href="{{ url_for('profile.profile') }}" class="btn btn-light me-2">Profile</a>
          <a href="{{ url_for('blog.new_post') }}" class="btn btn-light me-2">New Post</a>
          <a href="{{ url_for('messaging.inbox') }}" class="btn btn-light me-2">Inbox</a>
          <a href="{{ url_for('auth.logout') }}" class="btn btn-light">Logout</a>
        {% else %}
          <a href="{{ url_for('auth.login') }}" class="btn btn-light me-2">Login</a>
          <a href="{{ url_for('auth.register') }}" class="btn btn-light">Register</a>
        {% endif %}
      </div>
    </div>
  </nav>
  <div class="container">
      {% with messages = get_flashed_messages() %}
        {% if messages %}
          {% for message in messages %}
            <div class="alert alert-warning">{{ message }}</div>
          {% endfor %}
        {% endif %}
      {% endwith %}
      {% block content %}{% endblock %}
  </div>
</body>
</html>