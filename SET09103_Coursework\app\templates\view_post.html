{% extends 'base.html' %}
{% block content %}
<div class="p-4 mb-4 bg-dark border rounded text-center text-white">
  <h1 class="display-5 fw-bold">{{ post.title }}</h1>
  <p class="lead">By <a href="{{ url_for('profile.profile_view', user_id=post.author.id) }}" class="text-decoration-none text-white">{{ post.author.username }}</a></p>
  {% if current_user.is_authenticated and current_user.id == post.user_id %}
    <div class="mt-3">
      <a href="{{ url_for('blog.edit_post', post_id=post.id) }}" class="btn btn-light btn-sm me-2">Edit Post</a>
      <form method="post" action="{{ url_for('blog.delete_post', post_id=post.id) }}" style="display: inline;"
            onsubmit="return confirm('Are you sure you want to delete this post?')">
        <button type="submit" class="btn btn-light btn-sm">Delete Post</button>
      </form>
    </div>
  {% endif %}
</div>
<img src="{{ url_for('static', filename='blog_image_uploads/' ~ post.image_file) }}" class="img-fluid mb-3 mx-auto d-block">
<div class="border p-3 mb-2 rounded shadow-sm bg-white">
  <p>{{ post.content }}</p>
</div>
{% endblock %}