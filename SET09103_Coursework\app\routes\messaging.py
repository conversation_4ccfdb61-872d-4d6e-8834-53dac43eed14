from flask import Blueprint, render_template, request, redirect, url_for, flash, abort
from flask_login import login_required, current_user
from app import db
from app.models import User, Message
from sqlalchemy import select

bp = Blueprint('messaging', __name__)

@bp.route('/inbox')
@login_required
def inbox():
    """Display current user's received messages, ordered by newest first."""
    messages = db.session.execute(
        select(Message).where(Message.recipient_id == current_user.id).order_by(Message.timestamp.desc())
    ).scalars().all()
    return render_template('inbox.html', messages=messages)

@bp.route('/send_message/<int:user_id>', methods=['GET', 'POST'])
@login_required
def send_message(user_id):
    """Send a message to a specific user by their ID."""
    recipient = db.session.execute(select(User).where(User.id == user_id)).scalar_one_or_none()
    if not recipient:
        abort(404)
    if request.method == 'POST':
        content = request.form['content']
        if content.strip():
            message = Message(content=content, sender_id=current_user.id, recipient_id=user_id)
            db.session.add(message)
            db.session.commit()
            flash('Message sent successfully!')
            return redirect(url_for('messaging.inbox'))
        flash('Message cannot be empty')
    return render_template('send_message.html', recipient=recipient)

@bp.route('/compose', methods=['GET', 'POST'])
@login_required
def compose():
    """Compose and send a message to any user by entering their username."""
    if request.method == 'POST':
        username = request.form['username']
        content = request.form['content']
        recipient = db.session.execute(select(User).where(User.username == username)).scalar_one_or_none()
        if not recipient:
            flash('User not found')
        elif recipient.id == current_user.id:
            flash('You cannot send a message to yourself')
        elif content.strip():
            message = Message(content=content, sender_id=current_user.id, recipient_id=recipient.id)
            db.session.add(message)
            db.session.commit()
            flash('Message sent successfully!')
            return redirect(url_for('messaging.inbox'))
        else:
            flash('Message cannot be empty')
    return render_template('compose_message.html')
