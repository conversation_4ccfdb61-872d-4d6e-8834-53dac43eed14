from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_user, logout_user, login_required
from app import db, bcrypt
from app.models import User
from werkzeug.utils import secure_filename
from datetime import datetime
from sqlalchemy import select
import os, uuid

bp = Blueprint('auth', __name__)


@bp.route('/login', methods=['GET', 'POST'])
def login():
    """Handle user login. Validates credentials and creates user session."""
    if request.method == 'POST':
        user = db.session.execute(select(User).where(User.username == request.form['username'])).scalar_one_or_none()
        if user and bcrypt.check_password_hash(user.password, request.form['password']):
            login_user(user)
            return redirect(url_for('blog.home'))
        flash('Invalid Login Details')
    return render_template('login.html')


@bp.route('/register', methods=['GET', 'POST'])
def register():
    """Handle user registration with validation and optional profile picture upload."""
    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']
        confirm_password = request.form['confirm_password']

        if password != confirm_password:
            flash('Passwords do not match. Please try again.')
            return render_template('register.html')

        if db.session.execute(select(User).where(User.username == username)).scalar_one_or_none():
            flash('Username already exists. Please choose a different one.')
            return render_template('register.html')

        if db.session.execute(select(User).where(User.email == email)).scalar_one_or_none():
            flash('Email address already registered. Please use a different email.')
            return render_template('register.html')

        profile_picture = None
        file = request.files.get('profile_picture')
        if file and file.filename:
            file_ext = os.path.splitext(secure_filename(file.filename))[1].lower()
            profile_picture = f"profile_{uuid.uuid4().hex}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{file_ext}"
            file.save(os.path.join(current_app.config['PROFILE_PICTURE_FOLDER'], profile_picture))

        user = User(
            username=username,
            email=email,
            password=bcrypt.generate_password_hash(password).decode('utf-8'),
            bio=request.form.get('bio', ''),
            profile_picture=profile_picture
        )
        db.session.add(user)
        db.session.commit()
        flash('Account created successfully')
        return redirect(url_for('auth.login'))

    return render_template('register.html')


@bp.route('/logout')
@login_required
def logout():
    """Log out the current user and redirect to home page."""
    logout_user()
    return redirect(url_for('blog.home'))
