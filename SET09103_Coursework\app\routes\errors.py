from flask import Blueprint, render_template

bp = Blueprint('errors', __name__)

@bp.app_errorhandler(404)
def page_not_found(e):
    """Handle 404 errors - page or resource not found."""
    return render_template('errors/404.html'), 404

@bp.app_errorhandler(401)
def unauthorised(e):
    """Handle 401 errors - user not authenticated."""
    return render_template('errors/401.html'), 401

@bp.app_errorhandler(403)
def forbidden(e):
    """Handle 403 errors - user lacks permission to access resource."""
    return render_template('errors/403.html'), 403
