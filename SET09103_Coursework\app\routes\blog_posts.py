from flask import Blueprint, render_template, request, redirect, url_for, abort, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app import db
from app.models import Post
from datetime import datetime
from sqlalchemy import select
import uuid
import os

bp = Blueprint('blog', __name__)


@bp.route('/')
def home():
    """Display all blog posts on the home page, from the newest post to the oldest."""
    posts = db.session.execute(select(Post).order_by(Post.date_posted.desc())).scalars().all()
    return render_template('home.html', posts=posts)


@bp.route('/post/<int:post_id>')
def view_post(post_id):
    """Display a single blog post using its ID."""
    post = db.session.execute(select(Post).where(Post.id == post_id)).scalar_one_or_none()
    if not post:
        abort(404)
    return render_template('view_post.html', post=post)


@bp.route('/new_post', methods=['GET', 'POST'])
@login_required
def new_post():
    """Create a new blog post with optional image upload. Requires authentication."""
    if request.method == 'POST':
        title = request.form['title']
        content = request.form['content']

        # Image upload handling
        file = request.files.get('image')
        if file and file.filename:
            original_filename = secure_filename(file.filename)
            file_ext = os.path.splitext(original_filename)[1].lower()
            unique_filename = f"post_{uuid.uuid4().hex}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{file_ext}"

            filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
            file.save(filepath)

            post = Post(
                title=title,
                content=content,
                image_file=unique_filename,
                user_id=current_user.id
            )
            db.session.add(post)
            db.session.commit()
            return redirect(url_for('blog.home'))

    return render_template('new_post.html')


@bp.route('/delete_post/<int:post_id>', methods=['POST'])
@login_required
def delete_post(post_id):
    """Delete a blog post. Only the author is able to delete their own posts."""
    post = db.session.execute(select(Post).where(Post.id == post_id)).scalar_one_or_none()
    if not post:
        abort(404)

    if post.user_id != current_user.id:
        abort(403)

    if post.image_file:
        image_path = os.path.join(current_app.config['UPLOAD_FOLDER'], post.image_file)
        if os.path.exists(image_path):
            os.remove(image_path)

    db.session.delete(post)
    db.session.commit()
    return redirect(url_for('profile.profile'))


@bp.route('/edit_post/<int:post_id>', methods=['GET', 'POST'])
@login_required
def edit_post(post_id):
    """Edit an existing blog post. Only the author is able to edit their own posts."""
    post = db.session.execute(select(Post).where(Post.id == post_id)).scalar_one_or_none()
    if not post:
        abort(404)

    if post.user_id != current_user.id:
        abort(403)

    if request.method == 'POST':
        post.title = request.form['title']
        post.content = request.form['content']
        db.session.commit()
        return redirect(url_for('blog.view_post', post_id=post.id))

    return render_template('edit_post.html', post=post)
