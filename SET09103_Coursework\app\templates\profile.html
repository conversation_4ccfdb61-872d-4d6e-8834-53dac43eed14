{% extends 'base.html' %}
{% block content %}
<div class="row">
  <div class="col-md-12">
    <div class="text-center mb-3">
      {% if user.profile_picture %}
        <img src="{{ url_for('static', filename='profile_picture_uploads/' ~ user.profile_picture) }}"
             class="img-fluid rounded-circle" style="width: 150px; height: 150px; object-fit: cover;"
             alt="Profile Picture">
      {% else %}
        <img src="{{ url_for('static', filename='profile_picture_uploads/' ~ 'default_profile_picture.webp') }}"
             class="img-fluid rounded-circle" style="width: 150px; height: 150px; object-fit: cover;"
             alt="Default Profile Picture">
      {% endif %}
    </div>
    <div class="border p-3 rounded bg-dark text-white text-center">
      <h1 class="fw-bold">{{ user.username }}</h1>
      <p>{{ user.bio or "This user has not provided a bio. How mysterious." }}</p>
      {% if is_own_profile %}
        <a href="{{ url_for('profile.edit_profile') }}" class="btn btn-light btn-sm">Edit Profile</a>
      {% endif %}
    </div>
  </div>
</div>

<div class="border p-3 mb-2 mt-4 rounded shadow-sm bg-white">
    <div class="">
      <div class="border-bottom pb-2 mb-3 text-center">
        <h1 class="fw-bold">My Photos</h1>
      </div>
      <div class="row">
        {% for post in posts %}
        <div class="col-md-4 mb-3">
          <div class="text-center p-4" style="height: 200px; position: relative;">
            <a href="{{ url_for('blog.view_post', post_id=post.id) }}">
              <img src="{{ url_for('static', filename='blog_image_uploads/' ~ post.image_file) }}"
                  class="img-fluid" style="max-height: 150px; object-fit: cover;"
                  alt="{{ post.title }}">
            </a>
          </div>
        </div>
        {% endfor %}
      </div>
  </div>
</div>

{% if is_own_profile %}
<div class="mt-4 text-center">
  <p class="text-muted">To delete your account, please enter your password below:</p>
  <form method="post" action="{{ url_for('profile.delete_account') }}"
        onsubmit="return confirm('Are you sure you want to delete your account? This action cannot be undone and will delete all your posts.')">
    <div class="d-inline-flex align-items-center">
      <input type="password" name="password" class="form-control me-2" style="width: 200px;" placeholder="Password" required>
      <button type="submit" class="btn btn-danger btn-sm">Delete Account</button>
    </div>
  </form>
</div>
{% endif %}
{% endblock %}