from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_bcrypt import Bcrypt
import os

db = SQLAlchemy()
login_manager = LoginManager()
bcrypt = Bcrypt()

def create_app():
    app = Flask(__name__)
    app.config.from_object('config.Config')

    db.init_app(app)
    login_manager.init_app(app)
    bcrypt.init_app(app)

    from app.routes import auth, blog_posts, profile, messaging, errors
    app.register_blueprint(auth.bp)
    app.register_blueprint(blog_posts.bp)
    app.register_blueprint(profile.bp)
    app.register_blueprint(messaging.bp)
    app.register_blueprint(errors.bp)

    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['PROFILE_PICTURE_FOLDER'], exist_ok=True)

    return app